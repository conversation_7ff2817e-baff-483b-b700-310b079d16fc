#include "servo.h"
#include <math.h>

/* 全局变量定义 */
ServoControl_t servo_control = {0};
float rectangle_speed = 1.0f; // 速度系数 (1.0 = 正常速度)
uint8_t rectangle_running = 0;

/* 私有变量 */
static uint32_t last_update_time = 0;
static const float PI = 3.14159265359f;

/**
 * @brief 舵机初始化
 */
void Servo_Init(void)
{
    // 启动PWM输出
    HAL_TIM_PWM_Start(&htim4, TIM_CHANNEL_3); // 底盘舵机
    HAL_TIM_PWM_Start(&htim4, TIM_CHANNEL_4); // 俯仰舵机
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_1); // 备用通道1
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_2); // 备用通道2

    // 初始化舵机到中心位置
    Servo_SetAngle(SERVO_BASE, SERVO_BASE_MAX_ANGLE / 2);   // 135度
    Servo_SetAngle(SERVO_PITCH, SERVO_PITCH_MAX_ANGLE / 2); // 90度

    // 延时等待舵机到位
    HAL_Delay(1000);
}

/**
 * @brief 设置舵机角度
 * @param channel 舵机通道
 * @param angle 目标角度
 */
void Servo_SetAngle(ServoChannel_t channel, float angle)
{
    uint16_t pulse_width;

    if (channel == SERVO_BASE)
    {
        // 底盘舵机 (0-270度)
        if (angle < SERVO_BASE_MIN_ANGLE)
            angle = SERVO_BASE_MIN_ANGLE;
        if (angle > SERVO_BASE_MAX_ANGLE)
            angle = SERVO_BASE_MAX_ANGLE;

        // 将角度转换为脉宽 (0.5ms - 2.5ms)
        pulse_width = SERVO_MIN_PULSE + (uint16_t)((angle / SERVO_BASE_MAX_ANGLE) * (SERVO_MAX_PULSE - SERVO_MIN_PULSE));
        Servo_SetPWM(SERVO_BASE, pulse_width);
        servo_control.base_angle = angle;
    }
    else if (channel == SERVO_PITCH)
    {
        // 俯仰舵机 (0-180度)
        if (angle < SERVO_PITCH_MIN_ANGLE)
            angle = SERVO_PITCH_MIN_ANGLE;
        if (angle > SERVO_PITCH_MAX_ANGLE)
            angle = SERVO_PITCH_MAX_ANGLE;

        // 将角度转换为脉宽 (0.5ms - 2.5ms)
        pulse_width = SERVO_MIN_PULSE + (uint16_t)((angle / SERVO_PITCH_MAX_ANGLE) * (SERVO_MAX_PULSE - SERVO_MIN_PULSE));
        Servo_SetPWM(SERVO_PITCH, pulse_width);
        servo_control.pitch_angle = angle;
    }
}

/**
 * @brief 设置舵机PWM脉宽
 * @param channel 舵机通道
 * @param pulse_width 脉宽 (单位: us)
 */
void Servo_SetPWM(ServoChannel_t channel, uint16_t pulse_width)
{
    // 限制脉宽范围
    if (pulse_width < SERVO_MIN_PULSE)
        pulse_width = SERVO_MIN_PULSE;
    if (pulse_width > SERVO_MAX_PULSE)
        pulse_width = SERVO_MAX_PULSE;

    // 将微秒转换为定时器计数值
    // TIM4: 84MHz / 840 = 100kHz, 周期 = 2000, 所以 1us = 0.1 计数
    // TIM8: 168MHz / 1680 = 100kHz, 周期 = 2000, 所以 1us = 0.1 计数
    uint16_t ccr_value = pulse_width / 10; // 转换为计数值

    if (channel == SERVO_BASE)
    {
        // 使用TIM4_CH3 (PD14)
        __HAL_TIM_SET_COMPARE(&htim4, TIM_CHANNEL_3, ccr_value);
    }
    else if (channel == SERVO_PITCH)
    {
        // 使用TIM4_CH4 (PD15)
        __HAL_TIM_SET_COMPARE(&htim4, TIM_CHANNEL_4, ccr_value);
    }
}

/**
 * @brief 启动舵机PWM输出
 */
void Servo_Start(void)
{
    HAL_TIM_PWM_Start(&htim4, TIM_CHANNEL_3);
    HAL_TIM_PWM_Start(&htim4, TIM_CHANNEL_4);
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_1);
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_2);
}

/**
 * @brief 停止舵机PWM输出
 */
void Servo_Stop(void)
{
    HAL_TIM_PWM_Stop(&htim4, TIM_CHANNEL_3);
    HAL_TIM_PWM_Stop(&htim4, TIM_CHANNEL_4);
    HAL_TIM_PWM_Stop(&htim8, TIM_CHANNEL_1);
    HAL_TIM_PWM_Stop(&htim8, TIM_CHANNEL_2);
}

/**
 * @brief 矩形轨迹初始化
 */
void Rectangle_Init(void)
{
    servo_control.current_side = RECT_SIDE_TOP;
    servo_control.progress = 0.0f;
    servo_control.direction = 1; // 顺时针
    rectangle_speed = 1.0f;
    rectangle_running = 0;
    last_update_time = HAL_GetTick();

    // 设置初始位置到矩形左上角
    float x_cm, y_cm;
    Get_Rectangle_Point(RECT_SIDE_TOP, 0.0f, &x_cm, &y_cm);

    float base_angle, pitch_angle;
    Calculate_Rectangle_Angles(x_cm, y_cm, &base_angle, &pitch_angle);

    Servo_SetAngle(SERVO_BASE, base_angle);
    Servo_SetAngle(SERVO_PITCH, pitch_angle);
}

/**
 * @brief 矩形轨迹更新 (需要在主循环中调用)
 */
void Rectangle_Update(void)
{
    if (!rectangle_running)
        return;

    uint32_t current_time = HAL_GetTick();
    uint32_t delta_time = current_time - last_update_time;

    // 每10ms更新一次
    if (delta_time >= 10)
    {
        last_update_time = current_time;

        // 计算进度增量 (根据速度调整)
        float progress_increment = 0.002f * rectangle_speed; // 基础速度

        servo_control.progress += progress_increment;

        // 检查是否完成当前边
        if (servo_control.progress >= 1.0f)
        {
            servo_control.progress = 0.0f;

            // 移动到下一边 (顺时针)
            if (servo_control.direction == 1)
            {
                servo_control.current_side = (servo_control.current_side + 1) % 4;
            }
            else
            {
                servo_control.current_side = (servo_control.current_side + 3) % 4; // 逆时针
            }
        }

        // 获取当前位置
        float x_cm, y_cm;
        Get_Rectangle_Point((RectSide_t)servo_control.current_side, servo_control.progress, &x_cm, &y_cm);

        // 计算舵机角度
        float base_angle, pitch_angle;
        Calculate_Rectangle_Angles(x_cm, y_cm, &base_angle, &pitch_angle);

        // 设置舵机角度
        Servo_SetAngle(SERVO_BASE, base_angle);
        Servo_SetAngle(SERVO_PITCH, pitch_angle);
    }
}

/**
 * @brief 设置矩形轨迹速度
 * @param speed 速度系数 (1.0 = 正常速度)
 */
void Rectangle_SetSpeed(float speed)
{
    if (speed > 0.0f && speed <= 5.0f)
    {
        rectangle_speed = speed;
    }
}

/**
 * @brief 启动矩形轨迹
 */
void Rectangle_Start(void)
{
    rectangle_running = 1;
    last_update_time = HAL_GetTick();
}

/**
 * @brief 停止矩形轨迹
 */
void Rectangle_Stop(void)
{
    rectangle_running = 0;
}

/**
 * @brief 重置矩形轨迹到起始位置
 */
void Rectangle_Reset(void)
{
    Rectangle_Stop();
    Rectangle_Init();
}

/**
 * @brief 根据矩形上的坐标计算舵机角度
 * @param x_cm X坐标 (cm)
 * @param y_cm Y坐标 (cm)
 * @param base_angle 输出底盘角度
 * @param pitch_angle 输出俯仰角度
 */
void Calculate_Rectangle_Angles(float x_cm, float y_cm, float *base_angle, float *pitch_angle)
{
    // 计算水平角度 (底盘舵机)
    // 假设激光器在原点，矩形在前方
    float horizontal_angle = atan2f(x_cm, LASER_DISTANCE_CM) * 180.0f / PI;

    // 将角度转换为舵机角度 (0-270度)
    // 假设135度为正前方
    *base_angle = 135.0f + horizontal_angle;

    // 限制角度范围
    if (*base_angle < SERVO_BASE_MIN_ANGLE)
        *base_angle = SERVO_BASE_MIN_ANGLE;
    if (*base_angle > SERVO_BASE_MAX_ANGLE)
        *base_angle = SERVO_BASE_MAX_ANGLE;

    // 计算垂直角度 (俯仰舵机)
    float distance = sqrtf(x_cm * x_cm + LASER_DISTANCE_CM * LASER_DISTANCE_CM);
    float vertical_angle = atan2f(y_cm, distance) * 180.0f / PI;

    // 将角度转换为舵机角度 (0-180度)
    // 假设90度为水平
    *pitch_angle = 90.0f - vertical_angle;

    // 限制角度范围
    if (*pitch_angle < SERVO_PITCH_MIN_ANGLE)
        *pitch_angle = SERVO_PITCH_MIN_ANGLE;
    if (*pitch_angle > SERVO_PITCH_MAX_ANGLE)
        *pitch_angle = SERVO_PITCH_MAX_ANGLE;
}

/**
 * @brief 获取矩形边上的点坐标
 * @param side 矩形边
 * @param progress 进度 (0.0-1.0)
 * @param x_cm 输出X坐标
 * @param y_cm 输出Y坐标
 */
void Get_Rectangle_Point(RectSide_t side, float progress, float *x_cm, float *y_cm)
{
    float half_size = RECT_SIZE_CM / 2.0f;

    // 限制进度范围
    if (progress < 0.0f)
        progress = 0.0f;
    if (progress > 1.0f)
        progress = 1.0f;

    switch (side)
    {
    case RECT_SIDE_TOP: // 上边：从左到右
        *x_cm = -half_size + progress * RECT_SIZE_CM;
        *y_cm = half_size;
        break;

    case RECT_SIDE_RIGHT: // 右边：从上到下
        *x_cm = half_size;
        *y_cm = half_size - progress * RECT_SIZE_CM;
        break;

    case RECT_SIDE_BOTTOM: // 下边：从右到左
        *x_cm = half_size - progress * RECT_SIZE_CM;
        *y_cm = -half_size;
        break;

    case RECT_SIDE_LEFT: // 左边：从下到上
        *x_cm = -half_size;
        *y_cm = -half_size + progress * RECT_SIZE_CM;
        break;

    default:
        *x_cm = 0.0f;
        *y_cm = 0.0f;
        break;
    }
}