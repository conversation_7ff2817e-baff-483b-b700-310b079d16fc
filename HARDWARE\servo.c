/**
 * @file servo.c
 * @brief 二维舵机云台激光矩形轨迹控制模块实现文件
 * @details 实现舵机控制、矩形轨迹规划、角度计算等核心功能
 * <AUTHOR> Assistant
 * @date 2025-01-12
 * @version 1.0
 */

// 包含舵机控制模块头文件
#include "servo.h"
// 包含数学库，用于三角函数计算
#include <math.h>

/* ================================ 全局变量定义 ================================ */
/**
 * @brief 舵机控制状态全局变量
 * @note 初始化为全零，保存舵机当前状态和轨迹参数
 */
ServoControl_t servo_control = {0};

/**
 * @brief 矩形轨迹运动速度系数
 * @note 1.0表示标准速度，可以通过Rectangle_SetSpeed()函数修改
 */
float rectangle_speed = 1.0f; // 速度系数 (1.0 = 正常速度)

/**
 * @brief 矩形轨迹运行状态标志
 * @note 0表示停止，1表示正在运行
 */
uint8_t rectangle_running = 0;

/* ================================ 私有变量定义 ================================ */
/**
 * @brief 上次轨迹更新的时间戳
 * @note 用于控制轨迹更新的时间间隔，实现平滑运动
 */
static uint32_t last_update_time = 0;

/**
 * @brief 圆周率常量定义
 * @note 用于角度计算中的弧度与角度转换
 */
static const float PI = 3.14159265359f;

/* ================================ 舵机基础控制函数 ================================ */

/**
 * @brief 舵机系统初始化函数
 * @details 启动PWM输出，设置舵机到初始位置，等待舵机稳定
 * @param 无
 * @return 无
 * @note 必须在使用其他舵机函数前调用此函数
 */
void Servo_Init(void)
{

    // 启动定时器8通道3的PWM输出，控制底盘舵机(水平旋转)
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_3); // 底盘舵机，连接到PC8引脚

    // 启动定时器8通道4的PWM输出，控制俯仰舵机(垂直旋转)
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_4); // 俯仰舵机，连接到PC9引脚

    // 设置底盘舵机到中心位置(270度的一半=135度，即正前方)
    // Servo_SetAngle(SERVO_BASE, SERVO_BASE_MAX_ANGLE / 2); // 135度，正前方位置
    Servo_SetAngle(SERVO_BASE, 10);  // 0度，正前方位置
    Servo_SetAngle(SERVO_PITCH, 90); // 90度，水平位置

    // 设置俯仰舵机到中心位置(180度的一半=90度，即水平位置)
    // Servo_SetAngle(SERVO_PITCH, SERVO_PITCH_MAX_ANGLE / 2); // 90度，水平位置

    // 延时1秒等待舵机转动到指定位置并稳定
    HAL_Delay(1000); // 1000ms延时，确保舵机有足够时间到达目标位置
}

/**
 * @brief 设置指定舵机的角度
 * @details 将角度值转换为PWM脉宽，并更新舵机状态
 * @param channel 舵机通道选择(SERVO_BASE或SERVO_PITCH)
 * @param angle 目标角度值(度)
 * @return 无
 * @note 角度会自动限制在有效范围内
 */
void Servo_SetAngle(ServoChannel_t channel, float angle)
{
    uint16_t pulse_width; // 存储计算得到的PWM脉宽值

    // 判断是否为底盘舵机(水平旋转舵机)
    if (channel == SERVO_BASE)
    {
        // 限制底盘舵机角度在有效范围内(0-270度)
        if (angle < SERVO_BASE_MIN_ANGLE) // 如果角度小于最小值
            angle = SERVO_BASE_MIN_ANGLE; // 设置为最小角度0度
        if (angle > SERVO_BASE_MAX_ANGLE) // 如果角度大于最大值
            angle = SERVO_BASE_MAX_ANGLE; // 设置为最大角度270度

        // 将角度线性映射到脉宽范围(0.5ms - 2.5ms)
        // 公式: 脉宽 = 最小脉宽 + (角度/最大角度) * (最大脉宽-最小脉宽)
        pulse_width = SERVO_MIN_PULSE + (uint16_t)((angle / SERVO_BASE_MAX_ANGLE) * (SERVO_MAX_PULSE - SERVO_MIN_PULSE));

        // 调用PWM设置函数，实际输出PWM信号
        Servo_SetPWM(SERVO_BASE, pulse_width);

        // 更新全局状态结构体中的底盘角度值
        servo_control.base_angle = angle;
    }
    // 判断是否为俯仰舵机(垂直旋转舵机)
    else if (channel == SERVO_PITCH)
    {
        // 限制俯仰舵机角度在有效范围内(0-180度)
        if (angle < SERVO_PITCH_MIN_ANGLE) // 如果角度小于最小值
            angle = SERVO_PITCH_MIN_ANGLE; // 设置为最小角度0度
        if (angle > SERVO_PITCH_MAX_ANGLE) // 如果角度大于最大值
            angle = SERVO_PITCH_MAX_ANGLE; // 设置为最大角度180度

        // 将角度线性映射到脉宽范围(0.5ms - 2.5ms)
        // 公式: 脉宽 = 最小脉宽 + (角度/最大角度) * (最大脉宽-最小脉宽)
        pulse_width = SERVO_MIN_PULSE + (uint16_t)((angle / SERVO_PITCH_MAX_ANGLE) * (SERVO_MAX_PULSE - SERVO_MIN_PULSE));

        // 调用PWM设置函数，实际输出PWM信号
        Servo_SetPWM(SERVO_PITCH, pulse_width);

        // 更新全局状态结构体中的俯仰角度值
        servo_control.pitch_angle = angle;
    }
}

/**
 * @brief 设置舵机PWM脉宽
 * @details 将脉宽值转换为定时器比较值，直接控制PWM输出
 * @param channel 舵机通道选择(SERVO_BASE或SERVO_PITCH)
 * @param pulse_width PWM脉宽值，单位微秒(us)
 * @return 无
 * @note 脉宽会自动限制在500-2500us范围内
 */
void Servo_SetPWM(ServoChannel_t channel, uint16_t pulse_width)
{
    // 安全检查：限制脉宽在有效范围内，防止舵机损坏
    if (pulse_width < SERVO_MIN_PULSE) // 如果脉宽小于最小值500us
        pulse_width = SERVO_MIN_PULSE; // 限制为最小值500us
    if (pulse_width > SERVO_MAX_PULSE) // 如果脉宽大于最大值2500us
        pulse_width = SERVO_MAX_PULSE; // 限制为最大值2500us

    // 将微秒单位的脉宽转换为定时器计数值
    // TIM8时钟计算: 168MHz(APB2) / 1680(预分频) = 100kHz, 周期2000计数 = 20ms
    // 因此: 1微秒 = 0.1个计数值，所以除以10进行转换
    uint16_t ccr_value = pulse_width / 10; // 微秒转换为定时器计数值

    // 根据舵机通道选择对应的定时器通道
    if (channel == SERVO_BASE)
    {
        // 底盘舵机使用TIM8通道3，连接到PC8引脚
        __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_3, ccr_value);
    }
    else if (channel == SERVO_PITCH)
    {
        // 俯仰舵机使用TIM8通道4，连接到PC9引脚
        __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_4, ccr_value);
    }
}

/**
 * @brief 启动所有舵机的PWM输出
 * @details 启动定时器PWM功能，开始输出控制信号
 * @param 无
 * @return 无
 * @note 通常在系统初始化时调用一次即可
 */
void Servo_Start(void)
{
    // 启动TIM8通道3，控制底盘舵机
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_3);
    // 启动TIM8通道4，控制俯仰舵机
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_4);
    // 启动TIM8通道1，备用扩展通道
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_1);
    // 启动TIM8通道2，备用扩展通道
    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_2);
}

/**
 * @brief 停止所有舵机的PWM输出
 * @details 停止定时器PWM功能，舵机将失去控制信号
 * @param 无
 * @return 无
 * @note 停止后舵机可能会因失去控制信号而松动
 */
void Servo_Stop(void)
{
    // 停止TIM8通道3，底盘舵机失去控制
    HAL_TIM_PWM_Stop(&htim8, TIM_CHANNEL_3);
    // 停止TIM8通道4，俯仰舵机失去控制
    HAL_TIM_PWM_Stop(&htim8, TIM_CHANNEL_4);
    // 停止TIM8通道1，备用通道
    HAL_TIM_PWM_Stop(&htim8, TIM_CHANNEL_1);
    // 停止TIM8通道2，备用通道
    HAL_TIM_PWM_Stop(&htim8, TIM_CHANNEL_2);
}

/* ================================ 矩形轨迹控制函数 ================================ */

/**
 * @brief 矩形轨迹系统初始化
 * @details 设置轨迹参数初始值，将激光定位到矩形中心点作为起始位置
 * @param 无
 * @return 无
 * @note 必须在启动轨迹运动前调用此函数
 *       轨迹流程：中心点 → 沿对角线到左上角 → 沿边框顺时针绘制矩形
 */
void Rectangle_Init(void)
{
    // 设置轨迹初始状态为对角线移动阶段
    servo_control.current_state = RECT_STATE_DIAGONAL;

    // 设置目标边为矩形上边(对角线移动完成后开始绘制的边)
    servo_control.current_side = RECT_SIDE_TOP;

    // 设置当前阶段的进度为0(起始点)
    servo_control.progress = 0.0f;

    // 设置运动方向为顺时针(1=顺时针，0=逆时针)
    servo_control.direction = 1; // 顺时针方向运动

    // 设置运动速度为标准速度
    rectangle_speed = 1.0f;

    // 设置运行状态为停止(需要手动启动)
    rectangle_running = 0;

    // 记录当前时间作为轨迹更新的基准时间
    last_update_time = HAL_GetTick();

    // 设置激光起始位置为矩形中心点(0, 0)
    float x_cm = 0.0f; // 矩形中心X坐标
    float y_cm = 0.0f; // 矩形中心Y坐标

    // 根据中心点坐标计算对应的舵机角度
    float base_angle, pitch_angle; // 存储计算得到的角度值
    Calculate_Rectangle_Angles(x_cm, y_cm, &base_angle, &pitch_angle);

    // 设置舵机到中心位置
    Servo_SetAngle(SERVO_BASE, base_angle);   // 设置底盘舵机角度(应为135°，正前方)
    Servo_SetAngle(SERVO_PITCH, pitch_angle); // 设置俯仰舵机角度(应为90°，水平方向)
}

/**
 * @brief 矩形轨迹位置更新函数
 * @details 根据时间和速度更新激光在矩形边框上的位置，需要在主循环中持续调用
 * @param 无
 * @return 无
 * @note 此函数必须在主循环中持续调用以实现平滑运动
 */
void Rectangle_Update(void)
{
    // 检查轨迹是否正在运行，如果未运行则直接返回
    if (!rectangle_running)
        return;

    // 获取当前系统时间(毫秒)
    uint32_t current_time = HAL_GetTick();

    // 计算距离上次更新的时间间隔
    uint32_t delta_time = current_time - last_update_time;

    // 控制更新频率：每10毫秒更新一次位置，确保运动平滑
    if (delta_time >= 10)
    {
        // 更新时间基准点
        last_update_time = current_time;

        // 计算本次更新的进度增量
        // 基础进度增量0.002表示每次更新移动0.2%的距离
        // 乘以速度系数实现速度控制
        float progress_increment = 0.002f * rectangle_speed; // 基础速度乘以速度系数

        // 累加进度值，推进激光位置
        servo_control.progress += progress_increment;

        // 声明坐标变量
        float x_cm, y_cm; // 存储计算得到的坐标

        // 根据当前状态处理不同的运动阶段
        if (servo_control.current_state == RECT_STATE_DIAGONAL)
        {
            // 对角线移动阶段：从中心点(0,0)移动到左上角(-25,+25)
            if (servo_control.progress >= 1.0f)
            {
                // 对角线移动完成，切换到矩形绘制阶段
                servo_control.current_state = RECT_STATE_DRAWING;
                servo_control.progress = 0.0f; // 重置进度
            }

            // 获取对角线上的当前位置
            Get_Diagonal_Point(servo_control.progress, &x_cm, &y_cm);
        }
        else // RECT_STATE_DRAWING
        {
            // 矩形绘制阶段：沿边框运动
            if (servo_control.progress >= 1.0f)
            {
                // 当前边绘制完成，切换到下一条边
                servo_control.progress = 0.0f;

                // 根据运动方向切换到下一条边
                if (servo_control.direction == 1) // 顺时针方向
                {
                    // 顺时针：上边(0)→右边(1)→下边(2)→左边(3)→上边(0)
                    servo_control.current_side = (servo_control.current_side + 1) % 4;
                }
                else // 逆时针方向
                {
                    // 逆时针：上边(0)→左边(3)→下边(2)→右边(1)→上边(0)
                    // +3相当于-1，但避免了负数取模的问题
                    servo_control.current_side = (servo_control.current_side + 3) % 4; // 逆时针
                }
            }

            // 获取矩形边上的当前位置
            Get_Rectangle_Point((RectSide_t)servo_control.current_side, servo_control.progress, &x_cm, &y_cm);
        }

        // 根据目标坐标计算舵机需要转动到的角度
        float base_angle, pitch_angle; // 存储计算得到的角度
        Calculate_Rectangle_Angles(x_cm, y_cm, &base_angle, &pitch_angle);

        // 将计算得到的角度应用到舵机，实现激光指向目标位置
        Servo_SetAngle(SERVO_BASE, base_angle);   // 设置底盘舵机角度(水平方向)
        Servo_SetAngle(SERVO_PITCH, pitch_angle); // 设置俯仰舵机角度(垂直方向)
    }
}

/**
 * @brief 设置矩形轨迹运动速度
 * @details 调整激光沿矩形边框运动的速度，支持0.1到5.0倍速度
 * @param speed 速度系数(1.0表示标准速度，0.5表示半速，2.0表示双倍速度)
 * @return 无
 * @note 速度范围限制在0.1-5.0之间，超出范围的值会被忽略
 */
void Rectangle_SetSpeed(float speed)
{
    // 检查速度参数是否在有效范围内
    if (speed > 0.0f && speed <= 5.0f)
    {
        // 更新全局速度系数
        rectangle_speed = speed;
    }
    // 如果速度超出范围，则忽略此次设置，保持原有速度
}

/**
 * @brief 启动矩形轨迹自动运动
 * @details 设置运行标志并重置时间基准，开始自动轨迹运动
 * @param 无
 * @return 无
 * @note 启动后需要在主循环中持续调用Rectangle_Update()函数
 */
void Rectangle_Start(void)
{
    // 设置运行标志为1，使Rectangle_Update()函数开始工作
    rectangle_running = 1;

    // 重置时间基准点，确保运动从当前时刻开始计时
    last_update_time = HAL_GetTick();
}

/**
 * @brief 停止矩形轨迹运动
 * @details 清除运行标志，停止自动轨迹更新
 * @param 无
 * @return 无
 * @note 停止后激光会保持在当前位置，舵机仍然有控制信号
 */
void Rectangle_Stop(void)
{
    // 清除运行标志，Rectangle_Update()函数将不再执行轨迹更新
    rectangle_running = 0;
}

/**
 * @brief 重置矩形轨迹到初始状态
 * @details 停止当前运动并重新初始化轨迹参数，激光回到起始位置
 * @param 无
 * @return 无
 * @note 重置后需要重新调用Rectangle_Start()来启动运动
 */
void Rectangle_Reset(void)
{
    // 首先停止当前运动
    Rectangle_Stop();

    // 重新初始化轨迹参数，激光回到矩形中心点起始位置
    Rectangle_Init();
}

/* ================================ 数学计算函数 ================================ */

/**
 * @brief 根据目标坐标计算舵机角度
 * @details 将三维空间中的目标点坐标转换为两个舵机的角度值
 * @param x_cm 目标点X坐标，单位厘米(左负右正)
 * @param y_cm 目标点Y坐标，单位厘米(下负上正)
 * @param base_angle 输出参数，底盘舵机角度指针
 * @param pitch_angle 输出参数，俯仰舵机角度指针
 * @return 无
 * @note 坐标系原点为激光器位置，Z轴正方向为激光器到矩形的方向
 */
void Calculate_Rectangle_Angles(float x_cm, float y_cm, float *base_angle, float *pitch_angle)
{
    /* ========== 计算底盘舵机角度(水平旋转角度) ========== */

    // 使用反正切函数计算水平偏转角度
    // atan2f(x, z)计算从Z轴正方向到目标点的水平夹角
    // 激光器在原点(0,0,0)，目标矩形在Z轴正方向距离LASER_DISTANCE_CM处
    float horizontal_angle = atan2f(x_cm, LASER_DISTANCE_CM) * 180.0f / PI;

    // 将相对角度转换为舵机绝对角度
    // 底盘舵机135度位置对应正前方(Z轴正方向)
    // 正的horizontal_angle表示向右偏转，负的表示向左偏转
    *base_angle = 135.0f + horizontal_angle;

    // 安全限制：确保底盘舵机角度在有效范围内(0-270度)
    if (*base_angle < SERVO_BASE_MIN_ANGLE) // 如果小于最小角度
        *base_angle = SERVO_BASE_MIN_ANGLE; // 限制为最小角度
    if (*base_angle > SERVO_BASE_MAX_ANGLE) // 如果大于最大角度
        *base_angle = SERVO_BASE_MAX_ANGLE; // 限制为最大角度

    /* ========== 计算俯仰舵机角度(垂直旋转角度) ========== */

    // 计算激光器到目标点的水平距离(在XZ平面上的投影距离)
    float distance = sqrtf(x_cm * x_cm + LASER_DISTANCE_CM * LASER_DISTANCE_CM);

    // 使用反正切函数计算垂直偏转角度
    // atan2f(y, distance)计算从水平面到目标点的垂直夹角
    float vertical_angle = atan2f(y_cm, distance) * 180.0f / PI;

    // 将相对角度转换为舵机绝对角度
    // 俯仰舵机90度位置对应水平方向
    // 正的vertical_angle表示向上偏转，负的表示向下偏转
    // 使用减法是因为舵机角度增大表示向下转动
    *pitch_angle = 90.0f - vertical_angle;

    // 安全限制：确保俯仰舵机角度在有效范围内(0-180度)
    if (*pitch_angle < SERVO_PITCH_MIN_ANGLE) // 如果小于最小角度
        *pitch_angle = SERVO_PITCH_MIN_ANGLE; // 限制为最小角度
    if (*pitch_angle > SERVO_PITCH_MAX_ANGLE) // 如果大于最大角度
        *pitch_angle = SERVO_PITCH_MAX_ANGLE; // 限制为最大角度
}

/**
 * @brief 根据矩形边和进度获取对应的坐标点
 * @details 计算激光在矩形指定边上按进度移动时的精确坐标位置
 * @param side 矩形边选择(RECT_SIDE_TOP/RIGHT/BOTTOM/LEFT)
 * @param progress 在当前边上的进度(0.0表示起点，1.0表示终点)
 * @param x_cm 输出参数，计算得到的X坐标指针
 * @param y_cm 输出参数，计算得到的Y坐标指针
 * @return 无
 * @note 坐标系以矩形中心为原点，X轴水平(左负右正)，Y轴垂直(下负上正)
 */
void Get_Rectangle_Point(RectSide_t side, float progress, float *x_cm, float *y_cm)
{
    // 计算矩形半边长，用于坐标计算
    // 矩形边长为RECT_SIZE_CM，半边长为边长的一半
    float half_size = RECT_SIZE_CM / 2.0f;

    // 安全检查：限制进度值在有效范围内[0.0, 1.0]
    if (progress < 0.0f) // 如果进度小于0
        progress = 0.0f; // 限制为0(起点)
    if (progress > 1.0f) // 如果进度大于1
        progress = 1.0f; // 限制为1(终点)

    // 根据指定的矩形边计算对应的坐标点
    switch (side)
    {
    case RECT_SIDE_TOP: // 矩形上边：从左上角到右上角(从左到右)
        // X坐标：从-half_size线性变化到+half_size
        *x_cm = -half_size + progress * RECT_SIZE_CM;
        // Y坐标：固定为+half_size(矩形上边)
        *y_cm = half_size;
        break;

    case RECT_SIDE_RIGHT: // 矩形右边：从右上角到右下角(从上到下)
        // X坐标：固定为+half_size(矩形右边)
        *x_cm = half_size;
        // Y坐标：从+half_size线性变化到-half_size
        *y_cm = half_size - progress * RECT_SIZE_CM;
        break;

    case RECT_SIDE_BOTTOM: // 矩形下边：从右下角到左下角(从右到左)
        // X坐标：从+half_size线性变化到-half_size
        *x_cm = half_size - progress * RECT_SIZE_CM;
        // Y坐标：固定为-half_size(矩形下边)
        *y_cm = -half_size;
        break;

    case RECT_SIDE_LEFT: // 矩形左边：从左下角到左上角(从下到上)
        // X坐标：固定为-half_size(矩形左边)
        *x_cm = -half_size;
        // Y坐标：从-half_size线性变化到+half_size
        *y_cm = -half_size + progress * RECT_SIZE_CM;
        break;

    default: // 异常情况处理：如果边参数无效
        // 设置坐标为原点(矩形中心)
        *x_cm = 0.0f;
        *y_cm = 0.0f;
        break;
    }
}

/**
 * @brief 根据进度获取对角线上的坐标点
 * @details 计算从矩形中心点(0,0)到左上角(-25,+25)对角线上指定进度的坐标位置
 * @param progress 对角线移动进度(0.0表示中心点，1.0表示左上角)
 * @param x_cm 输出参数，计算得到的X坐标指针
 * @param y_cm 输出参数，计算得到的Y坐标指针
 * @return 无
 * @note 对角线从中心点(0,0)直线移动到左上角(-25,+25)
 */
void Get_Diagonal_Point(float progress, float *x_cm, float *y_cm)
{
    // 安全检查：限制进度值在有效范围内[0.0, 1.0]
    if (progress < 0.0f) // 如果进度小于0
        progress = 0.0f; // 限制为0(起点)
    if (progress > 1.0f) // 如果进度大于1
        progress = 1.0f; // 限制为1(终点)

    // 计算矩形半边长
    float half_size = RECT_SIZE_CM / 2.0f; // 50cm / 2 = 25cm

    // 对角线起点：中心点(0, 0)
    float start_x = 0.0f;
    float start_y = 0.0f;

    // 对角线终点：左上角(-25, +25)
    float end_x = -half_size;
    float end_y = +half_size;

    // 线性插值计算当前位置
    // 公式：当前位置 = 起点 + 进度 × (终点 - 起点)
    *x_cm = start_x + progress * (end_x - start_x); // X坐标从0线性变化到-25
    *y_cm = start_y + progress * (end_y - start_y); // Y坐标从0线性变化到+25
}
