/**
 * @file servo.h
 * @brief 二维舵机云台激光矩形轨迹控制模块头文件
 * @details 该模块实现了使用两个舵机控制激光灯沿矩形边框运动的功能
 *          底盘舵机负责水平旋转(0-270度)，俯仰舵机负责垂直旋转(0-180度)
 * <AUTHOR> Assistant
 * @date 2025-01-12
 * @version 1.0
 */

#ifndef __SERVO_H__ // 防止头文件重复包含的宏定义开始
#define __SERVO_H__ // 定义头文件标识符

// 包含STM32 HAL库主头文件，提供基础数据类型和宏定义
#include "main.h"
// 包含定时器相关的头文件，用于PWM控制
#include "tim.h"

/* ================================ 舵机硬件参数定义 ================================ */
/**
 * @brief 舵机PWM信号频率定义
 * @note 标准舵机工作频率为50Hz，对应20ms周期
 */
#define SERVO_PWM_FREQ 50 // 舵机PWM频率 50Hz，这是舵机标准工作频率

/**
 * @brief PWM周期计数值定义
 * @note 基于定时器配置，2000个计数对应20ms周期
 */
#define SERVO_PWM_PERIOD 2000 // PWM周期计数值，对应20ms周期时间

/* ================================ 舵机脉宽控制参数 ================================ */
/**
 * @brief 舵机控制脉宽范围定义 (单位: 微秒)
 * @note 舵机通过脉宽调制(PWM)控制角度，不同脉宽对应不同角度
 */
#define SERVO_MIN_PULSE 500     // 最小脉宽0.5ms，对应舵机最小角度位置
#define SERVO_MAX_PULSE 2500    // 最大脉宽2.5ms，对应舵机最大角度位置
#define SERVO_CENTER_PULSE 1500 // 中心脉宽1.5ms，对应舵机中间位置

/* ================================ 舵机角度范围定义 ================================ */
/**
 * @brief 底盘舵机角度范围定义
 * @note 底盘舵机用于水平旋转，控制激光的左右移动
 */
#define SERVO_BASE_MIN_ANGLE 0   // 底盘舵机最小角度，完全向左
#define SERVO_BASE_MAX_ANGLE 270 // 底盘舵机最大角度270度，270度舵机的最大范围

/**
 * @brief 俯仰舵机角度范围定义
 * @note 俯仰舵机用于垂直旋转，控制激光的上下移动
 */
#define SERVO_PITCH_MIN_ANGLE 0   // 俯仰舵机最小角度，完全向下
#define SERVO_PITCH_MAX_ANGLE 180 // 俯仰舵机最大角度180度，180度舵机的最大范围

/* ================================ 矩形轨迹参数定义 ================================ */
/**
 * @brief 目标矩形的物理尺寸定义
 */
#define RECT_SIZE_CM 50 // 矩形边长50cm，定义激光要绘制的矩形大小

/**
 * @brief 激光器到目标矩形的距离
 * @note 这个距离影响角度计算，需要根据实际安装距离调整
 */
#define LASER_DISTANCE_CM 100 // 激光器到矩形平面的距离100cm

/* ================================ 枚举类型定义 ================================ */
/**
 * @brief 舵机通道枚举定义
 * @note 用于区分不同的舵机，便于函数调用时指定具体舵机
 */
typedef enum
{
    SERVO_BASE = 0, // 底盘舵机通道，控制水平旋转(左右移动)
    SERVO_PITCH = 1 // 俯仰舵机通道，控制垂直旋转(上下移动)
} ServoChannel_t;

/**
 * @brief 轨迹状态枚举定义
 * @note 定义轨迹运动的不同阶段，包括对角线移动和矩形边绘制
 */
typedef enum
{
    RECT_STATE_DIAGONAL = 0, // 对角线移动阶段，从中心点到左上角
    RECT_STATE_DRAWING = 1   // 矩形绘制阶段，沿边框运动
} RectState_t;

/**
 * @brief 矩形边枚举定义
 * @note 定义矩形的四条边，用于轨迹规划时确定当前绘制的边
 */
typedef enum
{
    RECT_SIDE_TOP = 0,    // 矩形上边，激光从左上角移动到右上角
    RECT_SIDE_RIGHT = 1,  // 矩形右边，激光从右上角移动到右下角
    RECT_SIDE_BOTTOM = 2, // 矩形下边，激光从右下角移动到左下角
    RECT_SIDE_LEFT = 3    // 矩形左边，激光从左下角移动到左上角
} RectSide_t;

/* ================================ 数据结构定义 ================================ */
/**
 * @brief 舵机控制状态结构体
 * @note 保存舵机当前状态和轨迹运动的相关参数
 */
typedef struct
{
    float base_angle;      // 底盘舵机当前角度值(0-270度范围)
    float pitch_angle;     // 俯仰舵机当前角度值(0-180度范围)
    uint8_t current_state; // 当前轨迹状态(0=对角线移动，1=矩形绘制)
    uint8_t current_side;  // 当前正在绘制的矩形边(0-3对应四条边)
    float progress;        // 当前阶段的进度(0.0表示起点，1.0表示终点)
    uint8_t direction;     // 运动方向标志(1=顺时针方向，0=逆时针方向)
} ServoControl_t;

/* ================================ 函数声明 ================================ */

/**
 * @brief 舵机基础控制函数组
 * @note 这些函数提供舵机的基本控制功能
 */
void Servo_Init(void);                                           // 初始化舵机系统，配置PWM输出
void Servo_SetAngle(ServoChannel_t channel, float angle);        // 设置指定舵机的角度
void Servo_SetPWM(ServoChannel_t channel, uint16_t pulse_width); // 设置指定舵机的PWM脉宽
void Servo_Start(void);                                          // 启动所有舵机的PWM输出
void Servo_Stop(void);                                           // 停止所有舵机的PWM输出

/**
 * @brief 矩形轨迹控制函数组
 * @note 这些函数实现激光沿矩形边框运动的自动控制
 */
void Rectangle_Init(void);            // 初始化矩形轨迹参数，设置起始位置
void Rectangle_Update(void);          // 更新矩形轨迹位置，需在主循环中持续调用
void Rectangle_SetSpeed(float speed); // 设置矩形轨迹运动速度
void Rectangle_Start(void);           // 启动矩形轨迹自动运动
void Rectangle_Stop(void);            // 停止矩形轨迹运动
void Rectangle_Reset(void);           // 重置矩形轨迹到初始状态

/**
 * @brief 数学计算函数组
 * @note 这些函数负责坐标转换和角度计算
 */
void Calculate_Rectangle_Angles(float x_cm, float y_cm, float *base_angle, float *pitch_angle); // 根据坐标计算舵机角度
void Get_Rectangle_Point(RectSide_t side, float progress, float *x_cm, float *y_cm);            // 获取矩形边上指定进度的坐标点
void Get_Diagonal_Point(float progress, float *x_cm, float *y_cm);                              // 获取对角线上指定进度的坐标点

/* ================================ 全局变量声明 ================================ */
/**
 * @brief 外部可访问的全局变量
 * @note 这些变量在servo.c中定义，其他文件可以通过extern访问
 */
extern ServoControl_t servo_control; // 舵机控制状态结构体，保存当前运动状态
extern float rectangle_speed;        // 矩形轨迹运动速度系数(1.0为标准速度)
extern uint8_t rectangle_running;    // 矩形轨迹运行状态标志(1=运行中，0=已停止)

#endif /* __SERVO_H__ */ // 防止头文件重复包含的宏定义结束
