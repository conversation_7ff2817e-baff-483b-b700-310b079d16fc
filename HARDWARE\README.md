# 二维舵机云台激光矩形轨迹控制模块

## 概述
这个模块实现了一个二维舵机云台系统，能够控制激光灯沿着50cm×50cm的矩形边框进行顺时针运动。

## 硬件配置
- **底盘舵机**: 270度舵机，使用TIM4通道3 (PD14)
- **俯仰舵机**: 180度舵机，使用TIM4通道4 (PD15)
- **备用通道**: TIM8通道1 (PC6) 和通道2 (PC7)
- **矩形尺寸**: 50cm × 50cm
- **激光距离**: 100cm (可调整)

## 文件结构
```
HARDWARE/
├── servo.h              # 头文件，包含所有函数声明和宏定义
├── servo.c              # 实现文件，包含所有功能函数
├── servo_example.c      # 使用示例
└── README.md           # 说明文档
```

## 主要功能

### 1. 舵机基本控制
- `Servo_Init()`: 初始化舵机系统
- `Servo_SetAngle()`: 设置舵机角度
- `Servo_Start()` / `Servo_Stop()`: 启动/停止PWM输出

### 2. 矩形轨迹控制
- `Rectangle_Init()`: 初始化矩形轨迹
- `Rectangle_Start()` / `Rectangle_Stop()`: 启动/停止轨迹运动
- `Rectangle_Update()`: 更新轨迹位置 (需在主循环中调用)
- `Rectangle_SetSpeed()`: 设置运动速度 (0.1-5.0倍)
- `Rectangle_Reset()`: 重置到起始位置

### 3. 角度计算
- `Calculate_Rectangle_Angles()`: 根据坐标计算舵机角度
- `Get_Rectangle_Point()`: 获取矩形边上的点坐标

## 使用方法

### 基本使用
```c
#include "servo.h"

int main(void) {
    // 系统初始化...
    
    // 1. 初始化舵机
    Servo_Init();
    
    // 2. 初始化矩形轨迹
    Rectangle_Init();
    
    // 3. 设置速度 (可选)
    Rectangle_SetSpeed(1.0f);  // 正常速度
    
    // 4. 启动运动
    Rectangle_Start();
    
    // 5. 主循环
    while(1) {
        Rectangle_Update();  // 必须持续调用
        HAL_Delay(1);
    }
}
```

### 手动控制舵机
```c
// 设置底盘舵机到135度 (正前方)
Servo_SetAngle(SERVO_BASE, 135.0f);

// 设置俯仰舵机到90度 (水平)
Servo_SetAngle(SERVO_PITCH, 90.0f);
```

### 速度控制
```c
Rectangle_SetSpeed(0.5f);  // 半速
Rectangle_SetSpeed(1.0f);  // 正常速度
Rectangle_SetSpeed(2.0f);  // 双倍速度
```

## 参数配置

### 舵机参数 (servo.h中可修改)
```c
#define SERVO_BASE_MAX_ANGLE    270     // 底盘舵机最大角度
#define SERVO_PITCH_MAX_ANGLE   180     // 俯仰舵机最大角度
#define SERVO_MIN_PULSE         500     // 最小脉宽 0.5ms
#define SERVO_MAX_PULSE         2500    // 最大脉宽 2.5ms
```

### 矩形参数
```c
#define RECT_SIZE_CM            50      // 矩形边长
#define LASER_DISTANCE_CM       100     // 激光到矩形的距离
```

## 运动轨迹说明

矩形轨迹按顺时针方向运动：
1. **上边**: 从左到右
2. **右边**: 从上到下  
3. **下边**: 从右到左
4. **左边**: 从下到上

坐标系统：
- 原点：激光器位置
- X轴：水平方向 (左负右正)
- Y轴：垂直方向 (下负上正)
- Z轴：深度方向 (激光到矩形的距离)

## 注意事项

1. **定时器配置**: 确保TIM4和TIM8已正确配置为PWM模式
2. **更新频率**: `Rectangle_Update()`需要在主循环中持续调用
3. **角度限制**: 舵机角度会自动限制在有效范围内
4. **速度范围**: 建议速度设置在0.1-5.0之间
5. **延时设置**: 主循环中建议添加1ms延时

## 故障排除

1. **舵机不动**: 检查PWM输出和舵机电源
2. **运动不平滑**: 减小`Rectangle_Update()`的调用间隔
3. **角度不准确**: 调整`Calculate_Rectangle_Angles()`中的参数
4. **速度太快/太慢**: 使用`Rectangle_SetSpeed()`调整

## 扩展功能

可以基于此模块扩展的功能：
- 椭圆轨迹
- 自定义多边形轨迹
- 激光功率控制
- 位置反馈
- 远程控制接口
