/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2025 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "tim.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "servo.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_TIM8_Init();
  /* USER CODE BEGIN 2 */

  /* ========== 锟斤拷维锟斤拷锟斤拷锟教�锟斤拷锟斤拷锟斤拷喂旒Ｏ低筹拷锟绞硷拷锟� ========== */

  // 锟斤拷锟斤拷1锟斤拷锟斤拷始锟斤拷锟斤拷锟斤拷锟斤拷锟较低�
  // 锟斤拷锟斤拷PWM锟斤拷锟斤拷锟斤拷锟斤拷枚锟斤拷锟斤拷锟斤拷锟斤拷位锟矫ｏ拷锟饺达拷锟斤拷锟斤拷榷锟�
  Servo_Init();

  // 锟斤拷锟斤拷2锟斤拷锟斤拷始锟斤拷锟斤拷锟轿轨迹锟斤拷锟斤拷
  // 锟斤拷锟矫轨迹锟斤拷始位锟斤拷为锟斤拷锟斤拷锟斤拷锟较角ｏ拷锟斤拷锟斤拷锟剿讹拷锟斤拷锟斤拷为顺时锟斤拷
  Rectangle_Init();

  // 锟斤拷锟斤拷3锟斤拷锟斤拷锟矫硷拷锟斤拷锟截撅拷锟轿边匡拷锟斤拷硕锟斤拷俣锟�
  // 1.0f锟斤拷示锟斤拷准锟劫度ｏ拷锟斤拷锟皆碉拷锟斤拷为0.1-5.0之锟斤拷锟斤拷锟斤拷锟街�
  // 0.5f = 锟斤拷锟劫ｏ拷2.0f = 双锟斤拷锟劫讹拷
  Rectangle_SetSpeed(1.0f);

  // 锟斤拷锟斤拷4锟斤拷锟斤拷锟斤拷锟斤拷锟轿轨迹锟皆讹拷锟剿讹拷
  // 锟斤拷锟解开始锟斤拷锟斤拷50cm锟斤拷50cm锟斤拷锟轿边匡拷顺时锟斤拷锟剿讹拷
  Rectangle_Start();

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

    /* ========== 锟斤拷循锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟铰硷拷锟斤拷旒Ｎ伙拷锟� ========== */

    // 锟斤拷锟侥猴拷锟斤拷锟斤拷锟斤拷锟铰撅拷锟轿轨迹位锟斤拷
    // 锟剿猴拷锟斤拷锟斤拷锟斤拷时锟斤拷锟斤拷俣燃锟斤拷慵わ拷獾鼻坝︼拷锟街革拷锟斤拷位锟斤拷
    // 锟斤拷锟皆讹拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟阶�锟斤拷锟斤拷锟斤拷应锟角讹拷
    // 锟斤拷锟斤拷锟斤拷锟斤拷循锟斤拷锟叫筹拷锟斤拷锟斤拷锟斤拷锟斤拷实锟斤拷平锟斤拷锟斤拷锟斤拷锟斤拷锟剿讹拷
    Rectangle_Update();

    // 锟斤拷锟斤拷锟斤拷时1锟斤拷锟诫，锟斤拷止CPU占锟斤拷锟绞癸拷锟斤拷
    // 同时为锟斤拷锟斤拷锟斤拷锟杰碉拷锟斤拷锟斤拷锟斤拷锟斤拷执锟斤拷时锟斤拷
    HAL_Delay(1);



    

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 4;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
