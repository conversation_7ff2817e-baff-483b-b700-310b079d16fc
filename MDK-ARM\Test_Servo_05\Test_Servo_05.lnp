--cpu=Cortex-M4.fp.sp
"test_servo_05\startup_stm32f407xx.o"
"test_servo_05\main.o"
"test_servo_05\gpio.o"
"test_servo_05\tim.o"
"test_servo_05\stm32f4xx_it.o"
"test_servo_05\stm32f4xx_hal_msp.o"
"test_servo_05\stm32f4xx_hal_tim.o"
"test_servo_05\stm32f4xx_hal_tim_ex.o"
"test_servo_05\stm32f4xx_hal_rcc.o"
"test_servo_05\stm32f4xx_hal_rcc_ex.o"
"test_servo_05\stm32f4xx_hal_flash.o"
"test_servo_05\stm32f4xx_hal_flash_ex.o"
"test_servo_05\stm32f4xx_hal_flash_ramfunc.o"
"test_servo_05\stm32f4xx_hal_gpio.o"
"test_servo_05\stm32f4xx_hal_dma_ex.o"
"test_servo_05\stm32f4xx_hal_dma.o"
"test_servo_05\stm32f4xx_hal_pwr.o"
"test_servo_05\stm32f4xx_hal_pwr_ex.o"
"test_servo_05\stm32f4xx_hal_cortex.o"
"test_servo_05\stm32f4xx_hal.o"
"test_servo_05\stm32f4xx_hal_exti.o"
"test_servo_05\system_stm32f4xx.o"
"test_servo_05\servo.o"
"test_servo_05\servo_example.o"
--strict --scatter "Test_Servo_05\Test_Servo_05.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "Test_Servo_05.map" -o Test_Servo_05\Test_Servo_05.axf