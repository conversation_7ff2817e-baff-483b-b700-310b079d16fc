/**
 * @file servo_example.c
 * @brief 舵机控制模块使用示例
 * <AUTHOR> Name
 * @date 2025-01-12
 */

#include "servo.h"

/**
 * @brief 舵机控制示例函数
 * 演示如何使用舵机控制模块
 */
void Servo_Example(void)
{
    // 1. 初始化舵机系统
    Servo_Init();
    
    // 2. 手动控制舵机角度示例
    // 设置底盘舵机到135度 (正前方)
    Servo_SetAngle(SERVO_BASE, 135.0f);
    HAL_Delay(1000);
    
    // 设置俯仰舵机到90度 (水平)
    Servo_SetAngle(SERVO_PITCH, 90.0f);
    HAL_Delay(1000);
    
    // 3. 矩形轨迹控制示例
    // 初始化矩形轨迹
    Rectangle_Init();
    
    // 设置运动速度 (1.0 = 正常速度, 可以是0.1-5.0)
    Rectangle_SetSpeed(1.5f);  // 1.5倍速度
    
    // 启动矩形轨迹运动
    Rectangle_Start();
    
    // 运行10秒
    uint32_t start_time = HAL_GetTick();
    while ((HAL_GetTick() - start_time) < 10000) {
        Rectangle_Update();  // 持续更新轨迹
        HAL_Delay(1);
    }
    
    // 停止运动
    Rectangle_Stop();
    
    // 4. 重置到起始位置
    Rectangle_Reset();
}

/**
 * @brief 自定义轨迹示例
 * 演示如何创建自定义的运动轨迹
 */
void Custom_Trajectory_Example(void)
{
    Servo_Init();
    
    // 创建一个简单的扫描轨迹
    for (int i = 0; i < 5; i++) {
        // 水平扫描
        for (float angle = 90.0f; angle <= 180.0f; angle += 1.0f) {
            Servo_SetAngle(SERVO_BASE, angle);
            HAL_Delay(20);
        }
        
        for (float angle = 180.0f; angle >= 90.0f; angle -= 1.0f) {
            Servo_SetAngle(SERVO_BASE, angle);
            HAL_Delay(20);
        }
        
        // 垂直移动
        float pitch = 90.0f + (i * 10.0f);
        if (pitch <= 180.0f) {
            Servo_SetAngle(SERVO_PITCH, pitch);
            HAL_Delay(500);
        }
    }
}

/**
 * @brief 速度控制示例
 * 演示如何动态调整运动速度
 */
void Speed_Control_Example(void)
{
    Servo_Init();
    Rectangle_Init();
    Rectangle_Start();
    
    uint32_t start_time = HAL_GetTick();
    
    while ((HAL_GetTick() - start_time) < 30000) {  // 运行30秒
        uint32_t elapsed = HAL_GetTick() - start_time;
        
        // 动态调整速度
        if (elapsed < 5000) {
            Rectangle_SetSpeed(0.5f);  // 慢速
        } else if (elapsed < 15000) {
            Rectangle_SetSpeed(1.0f);  // 正常速度
        } else if (elapsed < 25000) {
            Rectangle_SetSpeed(2.0f);  // 快速
        } else {
            Rectangle_SetSpeed(0.8f);  // 减速
        }
        
        Rectangle_Update();
        HAL_Delay(1);
    }
    
    Rectangle_Stop();
}

/**
 * @brief 角度计算测试
 * 测试角度计算函数的正确性
 */
void Angle_Calculation_Test(void)
{
    float base_angle, pitch_angle;
    
    // 测试矩形四个角的角度计算
    printf("矩形角点角度计算测试:\n");
    
    // 左上角
    Calculate_Rectangle_Angles(-25.0f, 25.0f, &base_angle, &pitch_angle);
    printf("左上角: Base=%.1f°, Pitch=%.1f°\n", base_angle, pitch_angle);
    
    // 右上角
    Calculate_Rectangle_Angles(25.0f, 25.0f, &base_angle, &pitch_angle);
    printf("右上角: Base=%.1f°, Pitch=%.1f°\n", base_angle, pitch_angle);
    
    // 右下角
    Calculate_Rectangle_Angles(25.0f, -25.0f, &base_angle, &pitch_angle);
    printf("右下角: Base=%.1f°, Pitch=%.1f°\n", base_angle, pitch_angle);
    
    // 左下角
    Calculate_Rectangle_Angles(-25.0f, -25.0f, &base_angle, &pitch_angle);
    printf("左下角: Base=%.1f°, Pitch=%.1f°\n", base_angle, pitch_angle);
}

/**
 * @brief 主要使用示例
 * 在main函数中调用此函数来运行示例
 */
void Run_Servo_Examples(void)
{
    // 选择要运行的示例 (取消注释相应的行)
    
    // Servo_Example();              // 基本舵机控制示例
    // Custom_Trajectory_Example();  // 自定义轨迹示例
    // Speed_Control_Example();      // 速度控制示例
    // Angle_Calculation_Test();     // 角度计算测试
}
